CREATE TABLE patient
(
    id                    SERIAL PRIMARY KEY,
    user_id               INTEGER references turnera_user (id),
    name                  <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    surname               <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    identification_number VARCHAR(255) NOT NULL,
    phone                 VARCHAR(255) NOT NULL,
    date_of_birth         DATE         NOT NULL,
    email                 VARCHAR(255),
    attendance_percentage DECIMAL      NOT NULL,
    created_by            INTEGER      NOT NULL,
    created_at            TIMESTAMP    NOT NULL,
    updated_by            INTEGER      NOT NULL,
    updated_at            TIMESTAMP    NOT NULL
);

alter table patient
    add column date_of_birth DATE NOT NULL;

alter table patient
    add column health_insurance_id INTEGER references health_insurance (id);

alter table patient
    alter column created_by set not null;
alter table patient
    alter column created_at set not null;
alter table patient
    alter column updated_by set not null;
alter table patient
    alter column updated_at set not null;
alter table patient
    alter column attendance_percentage set not null;
