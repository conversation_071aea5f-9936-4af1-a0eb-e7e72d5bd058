package com.turnera.turnera.blockedSlot.application.find;

import com.turnera.turnera.blockedSlot.domain.BlockedSlotService;
import com.turnera.turnera.blockedSlot.infrastructure.entities.BlockedSlot;
import jakarta.enterprise.context.ApplicationScoped;
import java.time.Month;
import java.time.Year;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@ApplicationScoped
@RequiredArgsConstructor
@Slf4j
public class FindBlockedSlotsForMedicalCenterAndProfessionalForMonthAndYear {

  private final BlockedSlotService blockedSlotService;

  public List<BlockedSlot> find(Integer medicalCenterId, Integer professionalId, Month month, Year year) {
    log.info(
        "Finding blocked slots for medical center: {}, professional: {}, month: {}, year: {}",
        medicalCenterId,
        professionalId,
        month,
        year);
    return blockedSlotService.findByMedicalCenterIdAndProfessionalIdAndMonthAndYear(
        medicalCenterId, professionalId, month, year);
  }
}
