package com.turnera.turnera.blockedSlot.infrastructure.adapter;

import com.turnera.turnera.blockedSlot.domain.BlockedSlotService;
import com.turnera.turnera.blockedSlot.infrastructure.entities.BlockedSlot;
import com.turnera.turnera.blockedSlot.infrastructure.repository.BlockedSlotRepository;
import jakarta.enterprise.context.ApplicationScoped;
import java.time.LocalDate;
import java.time.Month;
import java.time.Year;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import lombok.RequiredArgsConstructor;

@ApplicationScoped
@RequiredArgsConstructor
public class BlockedSlotServiceImpl implements BlockedSlotService {

  private final BlockedSlotRepository blockedSlotRepository;

  @Override
  public BlockedSlot save(BlockedSlot blockedSlot) {
    blockedSlotRepository.persist(blockedSlot);
    return blockedSlot;
  }

  @Override
  public Optional<BlockedSlot> findById(Integer id) {
    return blockedSlotRepository.findByIdOptional(id);
  }

  @Override
  public List<BlockedSlot> findByProfessionalIdAndMedicalCenterIdAndDate(
      Integer professionalId, Integer medicalCenterId, LocalDate date) {
    return blockedSlotRepository.findByProfessionalIdAndMedicalCenterIdAndDate(
        professionalId, medicalCenterId, date);
  }

  @Override
  public List<BlockedSlot> findByMedicalCenterIdAndProfessionalIdAndMonthAndYear(
      Integer medicalCenterId, Integer professionalId, Month month, Year year) {
    return blockedSlotRepository.findByMedicalCenterIdAndProfessionalIdAndMonthAndYear(
        medicalCenterId, professionalId, month.getValue(), year.getValue());
  }

  @Override
  public Set<BlockedSlot> findByMedicalCenterIdAndMonthAndYear(
      Integer medicalCenterId, Month month, Year year) {
    return blockedSlotRepository.findDistinctByMedicalCenterIdAndMonthAndYear(
        medicalCenterId, month.getValue(), year.getValue());
  }
}
