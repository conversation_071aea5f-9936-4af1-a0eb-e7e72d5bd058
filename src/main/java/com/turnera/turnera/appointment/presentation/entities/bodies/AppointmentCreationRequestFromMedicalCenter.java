package com.turnera.turnera.appointment.presentation.entities.bodies;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.turnera.turnera.appointment.domain.entities.AppointmentCreationInput;
import com.turnera.turnera.appointment.domain.entities.AppointmentSource;
import com.turnera.turnera.appointment.presentation.entities.validations.DateAndStartTimeAfterNow;
import com.turnera.turnera.consultationType.infrastructure.entities.ConsultationTypeProfessionalMedicalCenterRelationship;
import com.turnera.turnera.employeeUser.infrastructure.entities.EmployeeUser;
import com.turnera.turnera.patient.infrastructure.entities.Patient;
import com.turnera.turnera.utils.deserializers.CustomDateDeserializer;
import com.turnera.turnera.utils.deserializers.CustomTimeDeserializer;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.List;
import java.util.Optional;
import lombok.Getter;
import lombok.Setter;
import org.eclipse.microprofile.openapi.annotations.enums.SchemaType;
import org.eclipse.microprofile.openapi.annotations.media.Schema;

@Setter
@Getter
@DateAndStartTimeAfterNow
public class AppointmentCreationRequestFromMedicalCenter {

  @NotNull private Integer medicalCenterId;

  @NotNull private Integer professionalId;

  private AppointmentHealthInsuranceDTO healthInsuranceInformation;

  @NotNull private Integer patientId;

  @NotNull private List<Integer> consultationTypeIds;

  private String patientNotes;

  private BigDecimal price;
  @NotNull private Integer appointmentSlotDuration;

  @NotNull private Integer employeeUserId;

  @NotNull
  @Schema(type = SchemaType.STRING, format = "date", example = "25-12-2023")
  @JsonFormat(pattern = "dd-MM-yyyy")
  @JsonDeserialize(using = CustomDateDeserializer.class)
  private LocalDate date;

  @NotNull
  @Schema(type = SchemaType.STRING, format = "time", example = "08:30:00")
  @JsonDeserialize(using = CustomTimeDeserializer.class)
  private LocalTime startTime;

  public AppointmentCreationInput toDomain(
      Patient patient,
      List<ConsultationTypeProfessionalMedicalCenterRelationship>
          consultationTypeProfessionalMedicalCenterRelationships,
      AppointmentSource origin,
      EmployeeUser employeeUser) {
    return new AppointmentCreationInput(
        patient,
        consultationTypeProfessionalMedicalCenterRelationships,
        Optional.ofNullable(healthInsuranceInformation),
        patientNotes,
        Optional.ofNullable(price),
        appointmentSlotDuration,
        date,
        startTime,
        origin,
        employeeUser);
  }

  public Boolean multipleConsultationFieldsAreDefined() {
    return appointmentSlotDuration != null && price != null;
  }

  public Boolean hasMoreThanOneConsultationType() {
    return consultationTypeIds.size() > 1;
  }
}
