package com.turnera.turnera.appointment.presentation;

import com.turnera.turnera.appointment.application.make.MakeAppointment;
import com.turnera.turnera.appointment.application.modify.ModifyAppointment;
import com.turnera.turnera.appointment.application.validations.AppointmentValidations;
import com.turnera.turnera.appointment.domain.entities.AppointmentCreationInput;
import com.turnera.turnera.appointment.domain.entities.AppointmentModificationInput;
import com.turnera.turnera.appointment.domain.entities.AppointmentSource;
import com.turnera.turnera.appointment.domain.entities.errors.ConsultationTypeNotAvailableOnlineException;
import com.turnera.turnera.appointment.domain.entities.errors.MultipleConsultationTypeFieldsNotDefinedException;
import com.turnera.turnera.appointment.infrastructure.entities.Appointment;
import com.turnera.turnera.appointment.presentation.entities.bodies.AppointmentCreationRequestFromMedicalCenter;
import com.turnera.turnera.appointment.presentation.entities.bodies.AppointmentCreationRequestFromUser;
import com.turnera.turnera.appointment.presentation.entities.bodies.AppointmentModificationRequest;
import com.turnera.turnera.consultationType.application.validations.ConsultationTypeValidations;
import com.turnera.turnera.consultationType.infrastructure.entities.ConsultationTypeProfessionalMedicalCenterRelationship;
import com.turnera.turnera.employeeUser.application.validations.EmployeeUserValidations;
import com.turnera.turnera.employeeUser.infrastructure.entities.EmployeeUser;
import com.turnera.turnera.patient.application.validations.PatientValidations;
import com.turnera.turnera.patient.infrastructure.entities.Patient;
import com.turnera.turnera.turneraUser.infrastructure.entities.TurneraUser;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.transaction.Transactional;
import jakarta.validation.Valid;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import java.util.List;
import lombok.RequiredArgsConstructor;

@Path("/appointment")
@ApplicationScoped
@RequiredArgsConstructor
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
public class AppointmentController {

  private final ConsultationTypeValidations consultationTypeValidations;
  private final PatientValidations patientValidations;
  private final MakeAppointment makeAppointment;
  private final ModifyAppointment modifyAppointment;
  private final EmployeeUserValidations employeeUserValidations;
  private final AppointmentValidations appointmentValidations;

  @POST
  @Path("/from-user")
  @Transactional
  public void createAppointmentFromUser(@Valid AppointmentCreationRequestFromUser request) {
    Patient patient =
        patientValidations.verifyPatientExistsAndIsOwnedBy(
            request.getPatientId(), request.getUserId());
    TurneraUser user = patient.getUser();
    ConsultationTypeProfessionalMedicalCenterRelationship
        consultationTypeProfessionalMedicalCenterRelationship =
            consultationTypeValidations
                .verifyProfessionalMedicalCenterConsultationTypeRelationshipsExists(
                    List.of(request.getConsultationTypeId()),
                    request.getMedicalCenterId(),
                    request.getProfessionalId())
                .getFirst();
    if (!consultationTypeProfessionalMedicalCenterRelationship.getAvailableOnline()) {
      throw new ConsultationTypeNotAvailableOnlineException(
          request.getConsultationTypeId(),
          request.getMedicalCenterId(),
          request.getProfessionalId());
    }
    AppointmentCreationInput input =
        request.toDomain(patient, consultationTypeProfessionalMedicalCenterRelationship, user);
    makeAppointment.make(input);
  }

  @POST
  @Path("/from-medical-center/{origin}")
  @Transactional
  public void createAppointmentFromMedicalCenter(
      @PathParam("origin") AppointmentSource origin,
      @Valid AppointmentCreationRequestFromMedicalCenter request) {
    EmployeeUser employeeUser =
        employeeUserValidations.verifyEmployeeUserExistsAndHasPermissions(
            request.getEmployeeUserId(), request.getMedicalCenterId());
    Patient patient = patientValidations.verifyPatientExists(request.getPatientId());
    if (request.hasMoreThanOneConsultationType()
        && !request.multipleConsultationFieldsAreDefined()) {
      throw new MultipleConsultationTypeFieldsNotDefinedException();
    }
    List<ConsultationTypeProfessionalMedicalCenterRelationship>
        consultationTypeProfessionalMedicalCenterRelationships =
            consultationTypeValidations
                .verifyProfessionalMedicalCenterConsultationTypeRelationshipsExists(
                    request.getConsultationTypeIds(),
                    request.getMedicalCenterId(),
                    request.getProfessionalId());
    AppointmentCreationInput input =
        request.toDomain(
            patient, consultationTypeProfessionalMedicalCenterRelationships, origin, employeeUser);
    makeAppointment.make(input);
  }

  @PUT
  @Path("/modify")
  public void modifyAppointment(@Valid AppointmentModificationRequest request) {
    Appointment appointment =
        appointmentValidations.verifyAppointmentExists(request.getAppointmentId());
    EmployeeUser employeeUser =
        employeeUserValidations.verifyEmployeeUserExistsAndHasPermissions(
            request.getEmployeeUserId(), appointment.getMedicalCenter().getId());
    AppointmentModificationInput input = request.toDomain(appointment, employeeUser);
    modifyAppointment.modify(input);
  }
}
