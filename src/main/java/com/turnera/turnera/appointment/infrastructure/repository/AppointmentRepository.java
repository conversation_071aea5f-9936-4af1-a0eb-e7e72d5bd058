package com.turnera.turnera.appointment.infrastructure.repository;

import com.turnera.turnera.appointment.infrastructure.entities.Appointment;
import io.quarkus.hibernate.orm.panache.PanacheRepositoryBase;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.transaction.Transactional;
import java.time.LocalDate;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@ApplicationScoped
public class AppointmentRepository implements PanacheRepositoryBase<Appointment, Integer> {

  @Transactional
  public void migratePatientIdsToNewPatientId(List<Integer> oldPatientIds, Integer newPatientId) {
    update("patient.id = ?1 WHERE patient.id IN ?2", newPatientId, oldPatientIds);
  }

  public List<Appointment>
      findFutureAppointmentsByMedicalCenterIdAndProfessionalIdAndDateIsGreaterThanEqual(
          Integer medicalCenterId, Integer professionalId, LocalDate now) {
    return find(
            "medicalCenter.id = ?1 AND professional.id = ?2 AND date >= ?3",
            medicalCenterId,
            professionalId,
            now)
        .list();
  }

  public List<Appointment> findAppointmentsByMedicalCenterIdAndProfessionalIdAndDate(
      Integer medicalCenterId, Integer professionalId, LocalDate date) {
    return find(
            "medicalCenter.id = ?1 AND professional.id = ?2 AND date = ?3",
            medicalCenterId,
            professionalId,
            date)
        .list();
  }

  public List<Appointment> findByMedicalCenterIdAndDateAndConsultationTypeId(
      Integer medicalCenterId, LocalDate date, Integer consultationTypeId) {
    return find(
            "SELECT a FROM Appointment a JOIN a.consultationTypes ct WHERE a.medicalCenter.id = ?1 AND a.date = ?2 AND ct.id = ?3",
            medicalCenterId,
            date,
            consultationTypeId)
        .list();
  }

  public List<Appointment> findByMedicalCenterIdAndProfessionalIdAndDayOfWeek(
      Integer medicalCenterId, Integer professionalId, Integer dayValue) {
    return find(
            "SELECT a FROM Appointment a WHERE a.medicalCenter.id = ?1 AND a.professional.id = ?2 AND EXTRACT(DOW FROM a.date) = ?3",
            medicalCenterId,
            professionalId,
            dayValue)
        .list();
  }

  public List<Appointment> findByMedicalCenterIdAndProfessionalIdAndMonthAndYear(
      Integer medicalCenterId, Integer professionalId, Integer month, Integer year) {
    return find(
            "SELECT a FROM Appointment a WHERE a.medicalCenter.id = ?1 AND a.professional.id = ?2 AND EXTRACT(MONTH FROM a.date) = ?3 AND EXTRACT(YEAR FROM a.date) = ?4",
            medicalCenterId,
            professionalId,
            month,
            year)
        .list();
  }

  public Set<Appointment> findDistinctByMedicalCenterIdMonthAndYear(
      Integer medicalCenterId, Integer month, Integer year) {
    return find(
            "SELECT DISTINCT a FROM Appointment a WHERE a.medicalCenter.id = ?1 AND EXTRACT(MONTH FROM a.date) = ?2"
                + " AND EXTRACT(YEAR FROM a.date) = ?3",
            medicalCenterId,
            month,
            year)
        .stream()
        .collect(Collectors.toSet());
  }
}
