package com.turnera.turnera.appointment.infrastructure.entities;

import com.turnera.turnera.appointment.domain.entities.AppointmentSource;
import com.turnera.turnera.appointment.domain.entities.AppointmentStatus;
import com.turnera.turnera.appointment.infrastructure.listeners.AppointmentListener;
import com.turnera.turnera.appointment.presentation.entities.AppointmentDTO;
import com.turnera.turnera.appointment.presentation.entities.ShortenedAppointmentDTO;
import com.turnera.turnera.appointment.presentation.entities.bodies.AppointmentHealthInsuranceDTO;
import com.turnera.turnera.consultationType.infrastructure.entities.ConsultationType;
import com.turnera.turnera.healthInsurance.infrastructure.entities.HealthInsurance;
import com.turnera.turnera.medicalCenter.infrastructure.entities.MedicalCenter;
import com.turnera.turnera.patient.infrastructure.entities.Patient;
import com.turnera.turnera.patient.infrastructure.entities.PatientMedicalCenterRelationship;
import com.turnera.turnera.professional.infrastructure.entities.Professional;
import com.turnera.turnera.schedule.domain.entities.AppointmentScheduleDayInputData;
import com.turnera.turnera.schedule.infrastructure.entities.AppointmentSchedule;
import com.turnera.turnera.schedule.infrastructure.entities.SpecialSchedule;
import com.turnera.turnera.user.infrastructure.entities.User;
import com.turnera.turnera.utils.LocalTimeUtils;
import com.turnera.turnera.utils.databaseUtils.slot.Slot;
import jakarta.persistence.*;
import java.math.BigDecimal;
import java.time.*;
import java.util.LinkedHashSet;
import java.util.Optional;
import java.util.Set;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
@Entity
@Table(name = "appointment")
@EntityListeners(AppointmentListener.class)
@EqualsAndHashCode(onlyExplicitlyIncluded = true, callSuper = false)
public class Appointment extends Slot {
  @Id
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "appointment_id_gen")
  @SequenceGenerator(
      name = "appointment_id_gen",
      sequenceName = "appointment_id_seq",
      allocationSize = 1)
  @EqualsAndHashCode.Include
  @Column(name = "id", nullable = false)
  private Integer id;

  @Column(name = "patient_notes", length = Integer.MAX_VALUE)
  private String patientNotes;

  @ManyToOne(fetch = FetchType.LAZY, optional = false)
  @JoinColumn(name = "patient_id", nullable = false)
  private Patient patient;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "health_insurance_id")
  private HealthInsurance healthInsurance;

  @Column(name = "price")
  private BigDecimal price;

  @Enumerated(EnumType.STRING)
  @Column(name = "status", columnDefinition = "appointment_status not null", nullable = false)
  private AppointmentStatus state;

  @Column(name = "last_status_update")
  private LocalDateTime lastStatusUpdate;

  @Enumerated(EnumType.STRING)
  @Column(name = "source", columnDefinition = "appointment_source")
  private AppointmentSource source;

  @ManyToOne(fetch = FetchType.LAZY, optional = false)
  @JoinColumns({
    @JoinColumn(
        name = "patient_id",
        referencedColumnName = "patient_id",
        nullable = false,
        insertable = false,
        updatable = false),
    @JoinColumn(
        name = "medical_center_id",
        referencedColumnName = "medical_center_id",
        nullable = false,
        insertable = false,
        updatable = false)
  })
  private PatientMedicalCenterRelationship patientMedicalCenterRelationship;

  @ManyToMany(fetch = FetchType.LAZY)
  @JoinTable(
      name = "appointment_consultation_type_relationship",
      joinColumns = @JoinColumn(name = "appointment_id", insertable = false, updatable = false),
      inverseJoinColumns =
          @JoinColumn(name = "consultation_type_id", insertable = false, updatable = false))
  private Set<ConsultationType> consultationTypes = new LinkedHashSet<>();

  public Appointment() {}

  public Appointment(
      LocalDate date,
      LocalTime startTime,
      Integer appointmentIntervalAmount,
      String patientNotes,
      Patient patient,
      Professional professional,
      MedicalCenter medicalCenter,
      Optional<HealthInsurance> maybeHealthInsurance,
      BigDecimal price,
      AppointmentSource source,
      User creator) {
    this.setDate(date);
    this.setStartTime(startTime);
    this.setAppointmentIntervalAmount(appointmentIntervalAmount);
    this.patientNotes = patientNotes;
    this.patient = patient;
    this.setProfessional(professional);
    this.setProfessionalId(professional.getId().longValue());
    this.setMedicalCenter(medicalCenter);
    this.healthInsurance = maybeHealthInsurance.orElse(null);
    this.price = price;
    this.state = AppointmentStatus.PENDING;
    this.source = source;
    this.setCreatorType(creator.getUserType());
    this.setUpdaterType(creator.getUserType());
    this.setCreatedBy(creator.getId());
    this.setUpdatedBy(creator.getId());
  }

  public DayOfWeek getDayOfWeek() {
    return this.getDate()
        .atTime(getStartTime())
        .atZone(ZoneId.of("America/Argentina/Buenos_Aires"))
        .getDayOfWeek();
  }

  public boolean belongsToAppointmentSchedule(AppointmentScheduleDayInputData schedule) {
    return getDayOfWeek().equals(schedule.getDayOfWeek())
        && LocalTimeUtils.isBetweenTimes(
            getStartTime(), schedule.getStartTime(), schedule.getEndTime());
  }

  public boolean belongsToAppointmentSchedule(AppointmentSchedule schedule) {
    return getDayOfWeek().equals(schedule.getDay())
        && LocalTimeUtils.isBetweenTimes(
            getStartTime(), schedule.getStartTime(), schedule.getEndTime());
  }

  public boolean belongsToSpecialSchedule(SpecialSchedule schedule) {
    return getDate().equals(schedule.getDate())
        && LocalTimeUtils.isBetweenTimes(
            getStartTime(), schedule.getStartTime(), schedule.getEndTime());
  }

  public AppointmentHealthInsuranceDTO getAppointmentHealthInsuranceDTO() {
    return Optional.ofNullable(this.healthInsurance)
        .map(
            healthInsurance ->
                new AppointmentHealthInsuranceDTO(
                    healthInsurance.getName(), healthInsurance.getPlan()))
        .orElse(null);
  }

  public AppointmentDTO toDTO() {
    return new AppointmentDTO(
        this.getId().longValue(),
        this.getPatientId(),
        this.getPatient().getName() + " " + this.getPatient().getSurname(),
        this.getPatient().getIdentificationNumber(),
        this.getPatient().getPhone(),
        this.getPatient().getEmail(),
        Optional.ofNullable(this.healthInsurance).map(HealthInsurance::getId).orElse(null),
        getAppointmentHealthInsuranceDTO(),
        this.getPrice(),
        this.getState().toString(),
        this.getDate().toString(),
        this.getPatientNotes(),
        this.getStartTime().toString(),
        this.getAppointmentIntervalAmount(),
        this.getSource().toString(),
        this.getConsultationTypes().stream().map(ConsultationType::toDTO).toList(),
        this.getPatientMedicalCenterRelationship().getAppointmentCount(),
        this.getPatientMedicalCenterRelationship().getAttendancePercentage());
  }

  public boolean isNew() {
    return this.getState() == AppointmentStatus.PENDING;
  }

  public boolean isCompleted() {
    return this.getState() == AppointmentStatus.COMPLETE;
  }

  public boolean isCancelled() {
    return this.getState() == AppointmentStatus.CANCELLED;
  }

  public Integer getPatientId() {
    return this.patient.getId();
  }

  public ShortenedAppointmentDTO toShortenedDTO() {
    return new ShortenedAppointmentDTO(
        this.getId().longValue(),
        this.getDate(),
        this.getStartTime(),
        this.getConsultationTypes().stream().map(ConsultationType::toDTO).toList(),
        this.getState().toString(),
        this.getProfessional().getId());
  }

  public AppointmentConsultationTypeRelationship toConsultationTypeRelationship(
      ConsultationType consultationType) {
    AppointmentConsultationTypeRelationshipId id =
        new AppointmentConsultationTypeRelationshipId(this.getId(), consultationType.getId());
    return new AppointmentConsultationTypeRelationship(id, this, consultationType);
  }
}
