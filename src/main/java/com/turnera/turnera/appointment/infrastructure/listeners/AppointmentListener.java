package com.turnera.turnera.appointment.infrastructure.listeners;

import com.turnera.turnera.appointment.infrastructure.entities.Appointment;
import com.turnera.turnera.medicalCenter.application.sse.appointment.GetAppointmentDtoForSse;
import com.turnera.turnera.sse.application.medicalCenter.SseMedicalCenterEvents;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.enterprise.event.Event;
import jakarta.enterprise.event.Observes;
import jakarta.enterprise.event.TransactionPhase;
import jakarta.inject.Inject;
import jakarta.persistence.PostPersist;
import jakarta.persistence.PostRemove;
import jakarta.persistence.PostUpdate;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@ApplicationScoped
@RequiredArgsConstructor
@Slf4j
public class AppointmentListener {

  private final SseMedicalCenterEvents sseMedicalCenterEvents;
  private final GetAppointmentDtoForSse getAppointmentDtoForSse;

  @Inject Event<AppointmentEvent> appointmentEvent;

  @PostPersist
  @PostUpdate
  @PostRemove
  public void afterEvent(Appointment appointment) {
    appointmentEvent.fire(new AppointmentEvent(appointment));
  }

  @Transactional(Transactional.TxType.REQUIRES_NEW)
  public void handlePostCommitEvent(
      @Observes(during = TransactionPhase.AFTER_SUCCESS) AppointmentEvent event) {
    broadCastAppointmentUpdate(event.appointment());
  }

  private void broadCastAppointmentUpdate(Appointment appointment) {
    try {
      sseMedicalCenterEvents.broadCastEventCreator(
          () -> getAppointmentDtoForSse.get(appointment), appointment.getMedicalCenter().getId());
    } catch (Exception e) {
      log.error("Failed to broadcast patient update SSE event", e);
    }
  }

  public record AppointmentEvent(Appointment appointment) {}
}
