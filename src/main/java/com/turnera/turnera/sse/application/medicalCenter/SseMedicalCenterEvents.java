package com.turnera.turnera.sse.application.medicalCenter;

import static com.turnera.turnera.utils.LogMarkers.SSE;

import com.turnera.turnera.configuration.SseTaskScheduler;
import com.turnera.turnera.medicalCenter.application.sse.GetAllMedicalCenterInformationForSse;
import com.turnera.turnera.sse.domain.entities.SseEvent;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.validation.constraints.NotNull;
import jakarta.ws.rs.sse.OutboundSseEvent;
import jakarta.ws.rs.sse.Sse;
import jakarta.ws.rs.sse.SseEventSink;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;
import lombok.extern.slf4j.Slf4j;

@ApplicationScoped
@Slf4j
public class SseMedicalCenterEvents {

  private static final long HEARTBEAT_INTERVAL_SECONDS = 30;

  private final Map<Integer, List<SseEventSink>> sinksByMedicalCenter = new ConcurrentHashMap<>();
  private final Map<SseEventSink, ScheduledFuture<?>> heartbeatTasks = new ConcurrentHashMap<>();
  @Inject Sse sse;
  @Inject GetAllMedicalCenterInformationForSse getAllMedicalCenterInformationForSse;
  @Inject @SseTaskScheduler ScheduledExecutorService taskScheduler;

  public SseEventSink createEventSink(@NotNull Integer medicalCenterId, SseEventSink eventSink) {
    this.addEventSink(eventSink, medicalCenterId);

    List<SseEvent> initialEvents = getAllMedicalCenterInformationForSse.get(medicalCenterId);
    initialEvents.forEach(
        event -> {
          try {
            eventSink.send(event.toSendableEvent(sse));
          } catch (Exception e) {
            log.error(
                SSE, "Failed to send initial SSE event for medical center {}", medicalCenterId, e);
            removeEventSink(eventSink, medicalCenterId);
          }
        });

    return eventSink;
  }

  private void addEventSink(SseEventSink eventSink, Integer medicalCenterId) {
    sinksByMedicalCenter
        .computeIfAbsent(medicalCenterId, k -> new CopyOnWriteArrayList<>())
        .add(eventSink);
    startHeartbeat(eventSink, medicalCenterId);
    int totalSinks = sinksByMedicalCenter.values().stream().mapToInt(List::size).sum();
    log.info(
        SSE,
        "SSE event sink added for medical center {}. Sinks for this center: {}. Total sinks: {}",
        medicalCenterId,
        sinksByMedicalCenter.get(medicalCenterId).size(),
        totalSinks);
  }

  public void removeEventSink(SseEventSink eventSink, Integer medicalCenterId) {
    stopHeartbeat(eventSink);
    List<SseEventSink> sinks = sinksByMedicalCenter.get(medicalCenterId);
    if (sinks != null) {
      sinks.remove(eventSink);
      if (sinks.isEmpty()) {
        sinksByMedicalCenter.remove(medicalCenterId);
      }
    }
    if (!eventSink.isClosed()) {
      eventSink.close();
    }
    int totalSinks = sinksByMedicalCenter.values().stream().mapToInt(List::size).sum();
    log.info(
        SSE,
        "SSE event sink removed for medical center {}. Sinks for this center: {}. Total sinks: {}",
        medicalCenterId,
        sinks != null ? sinks.size() : 0,
        totalSinks);
  }

  public void broadcastEventToMedicalCenter(SseEvent event, Integer medicalCenterId) {
    List<SseEventSink> sinks = sinksByMedicalCenter.get(medicalCenterId);
    if (sinks == null || sinks.isEmpty()) {
      log.debug(SSE, "No event sinks found for medical center {}", medicalCenterId);
      return;
    }

    OutboundSseEvent sseEvent = event.toSendableEvent(sse);

    sendEventAndRemoveSinkIfFailed(medicalCenterId, sinks, sseEvent);

    if (sinks.isEmpty()) {
      sinksByMedicalCenter.remove(medicalCenterId);
    }
    log.info(
        SSE,
        "Broadcasted {} event to {} sinks for medical center {}",
        event.getEventType(),
        sinks.size(),
        medicalCenterId);
  }

  private void sendEventAndRemoveSinkIfFailed(
      Integer medicalCenterId, List<SseEventSink> sinks, OutboundSseEvent sseEvent) {
    sinks.removeIf(
        sink -> {
          try {
            if (!sink.isClosed()) {
              sink.send(sseEvent);
              return false;
            } else {
              return true; // Remove closed sink
            }
          } catch (Exception e) {
            log.error(SSE, "Failed to send SSE event to medical center {}", medicalCenterId, e);
            return true; // Remove failed sink
          }
        });
  }

  public void broadcastToAll(SseEvent event) {
    int totalSinks = 0;
    OutboundSseEvent sseEvent = event.toSendableEvent(sse);

    for (Map.Entry<Integer, List<SseEventSink>> entry : sinksByMedicalCenter.entrySet()) {
      Integer medicalCenterId = entry.getKey();
      List<SseEventSink> sinks = entry.getValue();

      sendEventAndRemoveSinkIfFailed(medicalCenterId, sinks, sseEvent);

      totalSinks += sinks.size();
    }

    sinksByMedicalCenter.entrySet().removeIf(entry -> entry.getValue().isEmpty());

    log.info(
        SSE,
        "Broadcasted {} event to {} sinks across all medical centers",
        event.getEventType(),
        totalSinks);
  }

  public Boolean hasEmittersForMedicalCenter(Integer medicalCenterId) {
    return sinksByMedicalCenter.containsKey(medicalCenterId)
        && !sinksByMedicalCenter.get(medicalCenterId).isEmpty();
  }

  public void broadCastEventCreator(Supplier<SseEvent> eventSupplier, Integer medicalCenterId) {
    if (!hasEmittersForMedicalCenter(medicalCenterId)) {
      return;
    }
    SseEvent event = eventSupplier.get();
    if (event != null) {
      broadcastEventToMedicalCenter(event, medicalCenterId);
    } else {
      log.warn(SSE, "No event created for medical center {}", medicalCenterId);
    }
  }

  private void startHeartbeat(SseEventSink eventSink, Integer medicalCenterId) {
    ScheduledFuture<?> heartbeatTask =
        taskScheduler.scheduleAtFixedRate(
            () -> sendHeartbeat(eventSink, medicalCenterId),
            HEARTBEAT_INTERVAL_SECONDS,
            HEARTBEAT_INTERVAL_SECONDS,
            TimeUnit.SECONDS);
    heartbeatTasks.put(eventSink, heartbeatTask);
    log.debug(SSE, "Heartbeat started for event sink in medical center {}", medicalCenterId);
  }

  private void stopHeartbeat(SseEventSink eventSink) {
    ScheduledFuture<?> heartbeatTask = heartbeatTasks.remove(eventSink);
    if (heartbeatTask != null) {
      heartbeatTask.cancel(true);
      log.debug(SSE, "Heartbeat stopped for event sink");
    }
  }

  private void sendHeartbeat(SseEventSink eventSink, Integer medicalCenterId) {
    try {
      if (!eventSink.isClosed()) {
        OutboundSseEvent heartbeat = sse.newEventBuilder().name("heartbeat").data("ping").build();
        eventSink.send(heartbeat);
        log.debug(SSE, "Heartbeat sent to medical center {}", medicalCenterId);
      } else {
        removeEventSink(eventSink, medicalCenterId);
      }
    } catch (Exception e) {
      log.warn(
          SSE,
          "Failed to send heartbeat to medical center {}, removing event sink",
          medicalCenterId,
          e);
      removeEventSink(eventSink, medicalCenterId);
    }
  }
}
