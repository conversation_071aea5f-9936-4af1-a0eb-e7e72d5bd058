package com.turnera.turnera.patient.infrastructure.entities;

import com.turnera.turnera.appointment.presentation.entities.ShortenedAppointmentDTO;
import com.turnera.turnera.healthInsurance.infrastructure.entities.HealthInsurance;
import com.turnera.turnera.patient.infrastructure.listeners.PatientListener;
import com.turnera.turnera.patient.presentation.entities.PatientDTO;
import com.turnera.turnera.patient.presentation.entities.PatientHealthInsuranceDTO;
import com.turnera.turnera.turneraUser.infrastructure.entities.TurneraUser;
import com.turnera.turnera.user.presentation.entities.TurneraPatientInformationDTO;
import com.turnera.turnera.utils.BuenosAiresTime;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import lombok.AccessLevel;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
@Entity
@Table(name = "patient")
@EntityListeners(PatientListener.class)
@EqualsAndHashCode
public class Patient {

  @Id
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "patient_id_seq")
  @SequenceGenerator(name = "patient_id_seq", sequenceName = "patient_id_seq", allocationSize = 1)
  @Column(name = "id", nullable = false)
  private Integer id;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "user_id")
  private TurneraUser user;

  @Size(max = 255)
  @NotNull
  @Column(name = "name", nullable = false)
  private String name;

  @Size(max = 255)
  @NotNull
  @Column(name = "surname", nullable = false)
  private String surname;

  @Size(max = 255)
  @NotNull
  @Column(name = "identification_number", nullable = false)
  private String identificationNumber;

  @Size(max = 255)
  @NotNull
  @Column(name = "phone", nullable = false)
  private String phone;

  @Size(max = 255)
  @Column(name = "email")
  private String email;

  @Column(name = "attendance_percentage")
  private BigDecimal attendancePercentage;

  @Column(name = "created_by")
  private Integer createdBy;

  @Column(name = "created_at")
  private LocalDateTime createdAt;

  @Column(name = "updated_by")
  private Integer updatedBy;

  @Column(name = "updated_at")
  private LocalDateTime updatedAt;

  @Column(name = "date_of_birth", nullable = false)
  private LocalDate dateOfBirth;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "health_insurance_id")
  @Getter(AccessLevel.NONE)
  private HealthInsurance healthInsurance;

  @OneToMany(mappedBy = "patient", fetch = FetchType.LAZY)
  private Set<PatientMedicalCenterRelationship> patientMedicalCenterRelationships =
      new LinkedHashSet<>();

  public Patient() {}

  public Patient(
      TurneraUser user,
      String name,
      String surname,
      String identificationNumber,
      String phone,
      String email,
      Optional<HealthInsurance> healthInsurance,
      Integer createdBy) {
    this.user = user;
    this.name = name;
    this.surname = surname;
    this.identificationNumber = identificationNumber;
    this.phone = phone;
    this.email = email;
    this.attendancePercentage = BigDecimal.valueOf(100);
    this.healthInsurance = healthInsurance.orElse(null);
    this.createdBy = createdBy;
    this.updatedBy = createdBy;
  }

  public Patient(
      String name,
      String surname,
      String identificationNumber,
      String phone,
      String email,
      LocalDate dateOfBirth,
      Optional<HealthInsurance> healthInsurance,
      Integer createdBy) {
    this.name = name;
    this.surname = surname;
    this.identificationNumber = identificationNumber;
    this.phone = phone;
    this.email = email;
    this.attendancePercentage = BigDecimal.valueOf(100);
    this.dateOfBirth = dateOfBirth;
    this.healthInsurance = healthInsurance.orElse(null);
    this.createdBy = createdBy;
    this.updatedBy = createdBy;
  }

  @PrePersist
  protected void onCreate() {
    LocalDateTime now = BuenosAiresTime.nowAsLocalDateTime();
    createdAt = now;
    updatedAt = now;
  }

  @PreUpdate
  protected void onUpdate() {
    updatedAt = BuenosAiresTime.nowAsLocalDateTime();
  }

  public Optional<HealthInsurance> getHealthInsurance() {
    return Optional.ofNullable(healthInsurance);
  }

  public TurneraPatientInformationDTO toResponse() {
    return new TurneraPatientInformationDTO(
        id,
        name,
        surname,
        identificationNumber,
        phone,
        email,
        attendancePercentage,
        dateOfBirth,
        getHealthInsurance().map(HealthInsurance::getFullName).orElse(null));
  }

  public PatientDTO toDTO(
      List<ShortenedAppointmentDTO> futureAppointments,
      List<ShortenedAppointmentDTO> pastAppointments) {
    return new PatientDTO(
        getId(),
        getName() + " " + getSurname(),
        getMaybeUserId().orElse(null),
        getIdentificationNumber(),
        getPhone(),
        getEmail(),
        getHealthInsuranceDto(),
        pastAppointments.size(),
        getAttendancePercentage(),
        futureAppointments,
        pastAppointments);
  }

  public Optional<Integer> getMaybeUserId() {
    return Optional.ofNullable(user).map(TurneraUser::getId);
  }

  public PatientHealthInsuranceDTO getHealthInsuranceDto() {
    return getHealthInsurance()
        .map(
            healthInsurance ->
                new PatientHealthInsuranceDTO(
                    healthInsurance.getId(), healthInsurance.getName(), healthInsurance.getPlan()))
        .orElse(null);
  }
}
