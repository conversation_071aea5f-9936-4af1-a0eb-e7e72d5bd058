package com.turnera.turnera.patient.infrastructure.adapter;

import com.turnera.turnera.healthInsurance.infrastructure.entities.HealthInsurance;
import com.turnera.turnera.medicalCenter.infrastructure.entities.MedicalCenter;
import com.turnera.turnera.patient.domain.PatientService;
import com.turnera.turnera.patient.domain.entities.PatientInput;
import com.turnera.turnera.patient.domain.entities.PatientInputByUser;
import com.turnera.turnera.patient.infrastructure.entities.Patient;
import com.turnera.turnera.patient.infrastructure.entities.PatientMedicalCenterRelationship;
import com.turnera.turnera.patient.infrastructure.entities.PatientMedicalCenterRelationshipId;
import com.turnera.turnera.patient.infrastructure.repository.PatientMedicalCenterRelationshipRepository;
import com.turnera.turnera.patient.infrastructure.repository.PatientRepository;
import com.turnera.turnera.turneraUser.infrastructure.entities.TurneraUser;
import com.turnera.turnera.utils.BuenosAiresTime;
import com.turnera.turnera.utils.UserType;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.persistence.EntityManager;
import java.util.List;
import java.util.Optional;
import lombok.RequiredArgsConstructor;

@ApplicationScoped
@RequiredArgsConstructor
public class PatientServiceImpl implements PatientService {

  private final PatientMedicalCenterRelationshipRepository
      patientMedicalCenterRelationshipRepository;
  private final PatientRepository patientRepository;
  private final EntityManager entityManager;

  @Override
  public Patient findById(Integer patientId) {
    return patientRepository.findById(patientId);
  }

  @Override
  public Patient createPatientForUser(PatientInputByUser patientInputByUser) {
    Patient patient = patientInputByUser.toPatient();
    patientRepository.persist(patient);
    return patient;
  }

  @Override
  public List<Patient> getPatientsByIdentificationNumberAndPhone(
      String identificationNumber, String phone) {
    return patientRepository.findAllByIdentificationNumberAndPhone(identificationNumber, phone);
  }

  @Override
  public void migrateOldPatientMedicalCenterRelationships(
      List<Integer> patientIds, Integer newPatientId) {
    patientMedicalCenterRelationshipRepository
        .findAllByPatientIdIn(patientIds)
        .forEach(
            relationship -> {
              Integer medicalCenterId = relationship.getMedicalCenter().getId();
              if (!patientMedicalCenterRelationshipRepository.existsByPatientIdAndMedicalCenterId(
                  newPatientId, medicalCenterId)) {
                patientMedicalCenterRelationshipRepository
                    .migrateRelationshipPatientIdToNewPatientId(
                        relationship.getPatient().getId(), newPatientId, medicalCenterId);
              } else {
                patientMedicalCenterRelationshipRepository.delete(relationship);
              }
            });
  }

  @Override
  public Optional<Patient> findPatientByUserIdAndIdentificationNumber(
      Integer userId, String identificationNumber) {
    return patientRepository.findByUserIdAndIdentificationNumber(userId, identificationNumber);
  }

  @Override
  public void deleteOldPatientProfiles(List<Integer> patientIds) {
    patientRepository.deleteAllByIdIn(patientIds);
  }

  @Override
  public Patient findsertPatientWithMaybeUser(PatientInput input, Optional<TurneraUser> maybeUser) {

    Optional<Patient> maybePatient =
        maybeUser.flatMap(
            user ->
                patientRepository.findByUserIdAndIdentificationNumber(
                    user.getId(), input.getIdentificationNumber()));
    return maybePatient.orElseGet(
        () -> {
          Patient patient = input.toPatient();
          patientRepository.persist(patient);
          return patient;
        });
  }

  @Override
  public void associateMedicalCenterToPatient(
      Integer medicalCenterId, Integer patientId, Integer userId, UserType creatorType) {
    if (!patientMedicalCenterRelationshipRepository.existsByPatientIdAndMedicalCenterId(
        patientId, medicalCenterId)) {
      PatientMedicalCenterRelationshipId medicalCenterRelationshipId =
          new PatientMedicalCenterRelationshipId(patientId, medicalCenterId);
      MedicalCenter medicalCenterProxy =
          entityManager.getReference(MedicalCenter.class, medicalCenterId);
      Patient patientProxy = entityManager.getReference(Patient.class, patientId);
      PatientMedicalCenterRelationship medicalCenterRelationship =
          new PatientMedicalCenterRelationship(
              medicalCenterRelationshipId, patientProxy, medicalCenterProxy, userId, creatorType);
      patientMedicalCenterRelationshipRepository.persist(medicalCenterRelationship);
    }
  }

  @Override
  public void updateRelationship(PatientMedicalCenterRelationship relationship) {
    patientMedicalCenterRelationshipRepository.persist(relationship);
  }

  @Override
  public PatientMedicalCenterRelationship findRelationshipByPatientIdAndMedicalCenterId(
      Integer patientId, Integer medicalCenterId) {
    return patientMedicalCenterRelationshipRepository.findById(
        new PatientMedicalCenterRelationshipId(patientId, medicalCenterId));
  }

  @Override
  public Patient associateHealthInsuranceToPatient(
      Integer patientId, Integer healthInsuranceId, Integer userId, UserType creatorType) {
    Patient patient = findById(patientId);
    patient.setHealthInsurance(
        entityManager.getReference(HealthInsurance.class, healthInsuranceId));
    patient.setUpdatedBy(userId);
    patient.setUpdatedAt(BuenosAiresTime.nowAsLocalDateTime());
    patientRepository.persist(patient);
    return patient;
  }
}
