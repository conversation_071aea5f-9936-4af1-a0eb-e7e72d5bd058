package com.turnera.turnera.patient.domain.entities;

import com.turnera.turnera.employeeUser.infrastructure.entities.EmployeeUser;
import com.turnera.turnera.healthInsurance.infrastructure.entities.HealthInsurance;
import com.turnera.turnera.patient.infrastructure.entities.Patient;
import java.time.LocalDate;
import java.util.Optional;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
@AllArgsConstructor
public class PatientInput {
  private Integer medicalCenterId;
  private EmployeeUser user;
  private String name;
  private String surname;
  private String identificationNumber;
  private String phone;
  private String email;
  private LocalDate dateOfBirth;

  private Optional<HealthInsurance> healthInsurance;



  public Patient toPatient() {
    return new Patient(
        this.name,
        this.surname,
        this.identificationNumber,
        this.phone,
        this.email,
        this.dateOfBirth,
        this.healthInsurance,
        this.user.getId());
  }
}
