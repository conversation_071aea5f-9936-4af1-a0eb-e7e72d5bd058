package com.turnera.turnera.patient.domain.errors;

import com.turnera.turnera.configuration.exceptions.ConflictException;

public class PatientAlreadyExistsException extends ConflictException {
  public PatientAlreadyExistsException(String identificationNumber, Integer userId) {
    super(
        "Patient with identification number %s already exists for user %s"
            .formatted(identificationNumber, userId));
  }
}
