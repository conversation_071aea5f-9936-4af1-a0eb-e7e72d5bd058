package com.turnera.turnera.patient.presentation.entities.bodies;

import jakarta.validation.constraints.NotBlank;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class PatientCreationRequestForUser {
  // Get<PERSON> and Setters
  @NotBlank(message = "Name is required")
  private String name;

  @NotBlank(message = "Surname is required")
  private String surname;

  @NotBlank(message = "Identification number is required")
  private String identificationNumber;

  private PatientHealthInsuranceCreationRequest healthInsuranceInformation;
}
