package com.turnera.turnera.patient.presentation.entities;

import com.turnera.turnera.appointment.presentation.entities.ShortenedAppointmentDTO;
import java.math.BigDecimal;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class PatientDTO {
  private Integer id;
  private String name;

  private Integer userId;

  private String identificationNumber;

  private String phone;
  private String email;
  private PatientHealthInsuranceDTO healthInsurance;

  private Integer attentionsInMedicalCenter;

  private BigDecimal attendancePercentage;

  private List<ShortenedAppointmentDTO> futureAppointments;

  private List<ShortenedAppointmentDTO> pastAppointments;
}
