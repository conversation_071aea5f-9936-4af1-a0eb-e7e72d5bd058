package com.turnera.turnera.patient.presentation.entities.bodies;

import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.annotation.Nullable;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import java.time.LocalDate;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class PatientCreationRequest {

  @NotBlank(message = "Name is required")
  @NotNull
  private String name;

  @NotNull
  @NotBlank(message = "Surname is required")
  private String surname;

  @NotNull
  @NotBlank(message = "Identification number is required")
  private String identificationNumber;

  @NotNull
  @JsonFormat(pattern = "dd/MM/yyyy")
  private LocalDate dateOfBirth;

  @NotNull
  @NotBlank(message = "Phone is required")
  @Pattern(
      regexp = "^\\+?[0-9]{10,15}$",
      message = "Phone number must be between 10 and 15 digits and may start with a '+'")
  private String phone;

  @Nullable
  @Email(message = "Email must be a valid email address")
  private String email;

  private PatientHealthInsuranceCreationRequest healthInsuranceInformation;
}
