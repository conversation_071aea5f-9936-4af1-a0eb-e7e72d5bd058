package com.turnera.turnera.patient.presentation.entities.bodies;

import com.turnera.turnera.healthInsurance.domain.entities.HealthInsuranceExistenceValidationInterface;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Setter
@Getter
@AllArgsConstructor
@NoArgsConstructor
public class PatientHealthInsuranceCreationRequest
    implements HealthInsuranceExistenceValidationInterface {
  private String name;
  private String plan;
}
