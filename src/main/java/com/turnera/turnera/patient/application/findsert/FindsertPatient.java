package com.turnera.turnera.patient.application.findsert;

import static com.turnera.turnera.utils.LogMarkers.APPLICATION;

import com.turnera.turnera.patient.domain.PatientService;
import com.turnera.turnera.patient.domain.entities.PatientInput;
import com.turnera.turnera.patient.infrastructure.entities.Patient;
import com.turnera.turnera.turneraUser.application.find.FindActiveTurneraUserByPhoneNumber;
import com.turnera.turnera.turneraUser.infrastructure.entities.TurneraUser;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.transaction.Transactional;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@ApplicationScoped
@Slf4j
@RequiredArgsConstructor
public class FindsertPatient {

  private final PatientService patientService;
  private final FindActiveTurneraUserByPhoneNumber findActiveTurneraUserByPhoneNumber;

  @Transactional
  public Patient findsert(PatientInput input) {
    log.info(
        APPLICATION,
        "Patient with identification {} from Medical Center {}",
        input.getIdentificationNumber(),
        input.getMedicalCenterId());
    Optional<TurneraUser> maybeUser = findActiveTurneraUserByPhoneNumber.find(input.getPhone());
    Patient patient = patientService.findsertPatientWithMaybeUser(input, maybeUser);
    log.info(APPLICATION, "Patient findserted with ID: {}", patient.getId());
    return patient;
  }
}
