package com.turnera.turnera.patient.application.associate;

import static com.turnera.turnera.utils.LogMarkers.APPLICATION;
import static com.turnera.turnera.utils.UserType.EMPLOYEE_USER;

import com.turnera.turnera.patient.application.findsert.FindsertPatient;
import com.turnera.turnera.patient.domain.entities.PatientInput;
import com.turnera.turnera.patient.infrastructure.entities.Patient;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@ApplicationScoped
@Slf4j
@RequiredArgsConstructor
public class AssociatePatientWithMedicalCenter {

  private final AssociateMedicalCenter associateMedicalCenter;
  private final FindsertPatient findsertPatient;

  @Transactional
  public Patient associate(PatientInput input) {
    log.info(
        APPLICATION,
        "Associating patient with identification {} to medical center {}",
        input.getIdentificationNumber(),
        input.getMedicalCenterId());
    Patient patient = findsertPatient.findsert(input);
    associateMedicalCenter.associate(
        input.getMedicalCenterId(), patient.getId(), input.getUser().getId(), EMPLOYEE_USER);
    log.info(
        APPLICATION,
        "Patient {} associated with Medical center {}",
        patient.getId(),
        input.getMedicalCenterId());
    return patient;
  }
}
