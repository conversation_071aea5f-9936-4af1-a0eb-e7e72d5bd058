package com.turnera.turnera.patient.application.find;

import com.turnera.turnera.patient.domain.PatientService;
import com.turnera.turnera.patient.infrastructure.entities.Patient;
import jakarta.enterprise.context.ApplicationScoped;
import java.util.Optional;
import lombok.RequiredArgsConstructor;

@ApplicationScoped
@RequiredArgsConstructor
public class FindMaybePatientByUserIdAndIdentificationNumber {
  private final PatientService patientService;

  public Optional<Patient> find(
      Integer userId, String identificationNumber) {
    return patientService.findPatientByUserIdAndIdentificationNumber(userId, identificationNumber);
  }
}
