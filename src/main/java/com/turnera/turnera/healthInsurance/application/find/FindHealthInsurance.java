package com.turnera.turnera.healthInsurance.application.find;

import static com.turnera.turnera.utils.LogMarkers.APPLICATION;

import com.turnera.turnera.appointment.presentation.entities.bodies.AppointmentHealthInsuranceDTO;
import com.turnera.turnera.healthInsurance.domain.HealthInsuranceService;
import com.turnera.turnera.healthInsurance.infrastructure.entities.HealthInsurance;
import jakarta.enterprise.context.ApplicationScoped;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@ApplicationScoped
@Slf4j
@RequiredArgsConstructor
public class FindHealthInsurance {

  private final HealthInsuranceService healthInsuranceService;

  public HealthInsurance find(Integer healthInsuranceId) {
    log.info(APPLICATION, "Finding health insurance ID: {}", healthInsuranceId);
    HealthInsurance healthInsurance = healthInsuranceService.findById(healthInsuranceId);
    log.info("Health insurance found with id: {}", healthInsurance);
    return healthInsurance;
  }

  public HealthInsurance findByHealthInsuranceInformation(
      AppointmentHealthInsuranceDTO healthInsuranceInformation) {
    log.info(
        APPLICATION,
        "Finding health insurance by name {} and plan {}",
        healthInsuranceInformation.getName(),
        healthInsuranceInformation.getPlan());
    HealthInsurance healthInsurance =
        healthInsuranceService.findByNameAndPlan(
            healthInsuranceInformation.getName(), healthInsuranceInformation.getPlan());
    log.info("Health insurance found with id: {}", healthInsurance.getId());
    return healthInsurance;
  }
}
