package com.turnera.turnera.healthInsurance.domain.errors;

import com.turnera.turnera.configuration.exceptions.NotFoundException;

public class HealthInsuranceNotFoundException extends NotFoundException {
  public HealthInsuranceNotFoundException(Integer healthInsuranceId) {
    super("Health insurance not found with ID: " + healthInsuranceId);
  }

  public HealthInsuranceNotFoundException(String healthInsuranceName, String plan) {
    super("Health insurance not found with name: " + healthInsuranceName + " and plan: " + plan);
  }
}
