package com.turnera.turnera.professional.domain;

import com.turnera.turnera.employeeUser.infrastructure.entities.EmployeeUser;
import com.turnera.turnera.professional.domain.entities.CreateProfessionalInput;
import com.turnera.turnera.professional.domain.entities.CreateProfessionalMedicalCenterRelationshipInput;
import com.turnera.turnera.professional.infrastructure.entities.Professional;
import com.turnera.turnera.professional.infrastructure.entities.ProfessionalMedicalCenterRelationship;
import java.util.Optional;

public interface ProfessionalService {

  Professional findById(Integer professionalId);

  Optional<ProfessionalMedicalCenterRelationship> findRelationshipByProfessionalAndMedicalCenter(
      Integer professionalId, Integer medicalCenterId);

  Professional create(CreateProfessionalInput input);

  ProfessionalMedicalCenterRelationship createProfessionalMedicalCenterRelationship(
      Professional professional,
      CreateProfessionalMedicalCenterRelationshipInput input,
      EmployeeUser createdBy);

  Professional findByMedicalLicense(String medicalLicense);

  Optional<Professional> findMaybeByAuth0Id(String auth0Id);
}
