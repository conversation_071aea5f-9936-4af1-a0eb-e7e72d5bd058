package com.turnera.turnera.professional.presentation.entities;

import java.util.List;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class ProfessionalDetailResponse {
  private PersonalInformation personalInformation;

  private List<String> specialties;

  private List<ProfessionalHealthInsurancesDTO> healthInsurances;

  private ScheduleInfo schedules;

  private List<ConsultationTypeDoctorRelationshipDTO> consultationTypes;

  private BookingPoliciesDTO bookingPoliciesDTO;
}
