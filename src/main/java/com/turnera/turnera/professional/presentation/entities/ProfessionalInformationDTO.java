package com.turnera.turnera.professional.presentation.entities;

import java.time.LocalTime;
import java.util.List;
import java.util.Optional;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ProfessionalInformationDTO {
  private Integer id;
  private String name;
  private String surname;
  private List<String> specialties;
  private String medicalLicense;
  private Long nextAppointment;
  private Optional<Long> nextAppointmentQuantity;
  private ProfessionalAgendaDTO agenda;
  private LocalTime appointmentIntervalTime;
  private List<ProfessionalHealthInsurancesDTO> healthInsurances;
  private List<ConsultationTypeDoctorRelationshipDTO> consultationTypes;
  private BookingPoliciesDTO bookingPolicies;
}
