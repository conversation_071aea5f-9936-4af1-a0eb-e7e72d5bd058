package com.turnera.turnera.professional.application.find;

import com.turnera.turnera.professional.domain.ProfessionalService;
import com.turnera.turnera.professional.infrastructure.entities.ProfessionalMedicalCenterRelationship;
import jakarta.enterprise.context.ApplicationScoped;
import java.util.Optional;
import lombok.RequiredArgsConstructor;

@ApplicationScoped
@RequiredArgsConstructor
public class FindProfessionalMedicalCenterRelationshipById {

  private final ProfessionalService professionalService;

  public Optional<ProfessionalMedicalCenterRelationship> find(
      Integer professionalId, Integer medicalCenterId) {
    return professionalService.findRelationshipByProfessionalAndMedicalCenter(
        professionalId, medicalCenterId);
  }
}
