package com.turnera.turnera.professional.application.details;

import com.turnera.turnera.consultationType.application.find.FindConsultationTypeMedicalCenterRelationshipsByProfessionalAndMedicalCenter;
import com.turnera.turnera.consultationType.infrastructure.entities.ConsultationTypeProfessionalMedicalCenterRelationship;
import com.turnera.turnera.professional.application.validations.ProfessionalValidations;
import com.turnera.turnera.professional.domain.utils.ProfessionalDetailUtils;
import com.turnera.turnera.professional.infrastructure.entities.ProfessionalMedicalCenterRelationship;
import com.turnera.turnera.professional.presentation.entities.*;
import com.turnera.turnera.schedule.application.find.FindScheduleInfoForProfessionalAndMedicalCenter;
import com.turnera.turnera.specialty.application.find.FindSpecialtiesRelationshipsByProfessionalAndMedicalCenter;
import com.turnera.turnera.specialty.infrastructure.entities.SpecialtyProfessionalMedicalCenterRelationship;
import jakarta.enterprise.context.ApplicationScoped;
import java.util.List;
import lombok.RequiredArgsConstructor;

@ApplicationScoped
@RequiredArgsConstructor
public class ProfessionalDetailService {

  private final ProfessionalValidations professionalValidations;

  private final ProfessionalDetailUtils professionalDetailUtils;

  private final FindSpecialtiesRelationshipsByProfessionalAndMedicalCenter
      findSpecialtiesRelationshipsByProfessionalAndMedicalCenter;

  private final FindScheduleInfoForProfessionalAndMedicalCenter
      findScheduleInfoForProfessionalAndMedicalCenter;

  private final FindConsultationTypeMedicalCenterRelationshipsByProfessionalAndMedicalCenter
      findConsultationTypeMedicalCenterRelationshipsByProfessionalAndMedicalCenter;

  public ProfessionalDetailResponse getProfessionalDetail(
      Integer professionalId, Integer medicalCenterId) {
    ProfessionalMedicalCenterRelationship medicalCenterRelationship =
        professionalValidations.verifyProfessionalBelongsToMedicalCenter(
            professionalId, medicalCenterId);
    List<SpecialtyProfessionalMedicalCenterRelationship> specialties =
        findSpecialtiesRelationshipsByProfessionalAndMedicalCenter.find(
            professionalId, medicalCenterId);
    List<String> specialtiesNames =
        specialties.stream()
            .map(SpecialtyProfessionalMedicalCenterRelationship::getSpecialtyName)
            .toList();
    List<ProfessionalHealthInsurancesDTO> professionalHealthInsurancesDTO =
        professionalDetailUtils.getHealthInsuranceInformation(professionalId, medicalCenterId);
    ScheduleInfo scheduleInfo =
        findScheduleInfoForProfessionalAndMedicalCenter.find(professionalId, medicalCenterId);
    List<ConsultationTypeDoctorRelationshipDTO> consultationTypeDoctorRelationshipDTO =
        findConsultationTypeMedicalCenterRelationshipsByProfessionalAndMedicalCenter
            .find(professionalId, medicalCenterId)
            .stream()
            .map(ConsultationTypeProfessionalMedicalCenterRelationship::toDto)
            .toList();

    return ProfessionalDetailResponse.builder()
        .personalInformation(medicalCenterRelationship.toPersonalInformation())
        .bookingPoliciesDTO(medicalCenterRelationship.toBookingPoliciesDTO())
        .specialties(specialtiesNames)
        .schedules(scheduleInfo)
        .healthInsurances(professionalHealthInsurancesDTO)
        .consultationTypes(consultationTypeDoctorRelationshipDTO)
        .build();
  }
}
