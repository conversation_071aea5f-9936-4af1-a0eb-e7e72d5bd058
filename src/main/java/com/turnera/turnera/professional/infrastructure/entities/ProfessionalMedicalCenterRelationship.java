package com.turnera.turnera.professional.infrastructure.entities;

import com.turnera.turnera.consultationType.infrastructure.entities.ConsultationTypeProfessionalMedicalCenterRelationship;
import com.turnera.turnera.employeeUser.infrastructure.entities.EmployeeUser;
import com.turnera.turnera.healthInsurance.infrastructure.entities.HealthInsuranceProfessionalMedicalCenterRelationship;
import com.turnera.turnera.medicalCenter.infrastructure.entities.MedicalCenter;
import com.turnera.turnera.professional.domain.entities.CreateProfessionalMedicalCenterRelationshipInput;
import com.turnera.turnera.professional.domain.entities.OverlappedAppointmentLimit;
import com.turnera.turnera.professional.domain.errors.MaximumAnticipationTimeLimitException;
import com.turnera.turnera.professional.domain.errors.MinimumAnticipationTimeLimitException;
import com.turnera.turnera.professional.presentation.entities.BookingPoliciesDTO;
import com.turnera.turnera.professional.presentation.entities.PersonalInformation;
import com.turnera.turnera.schedule.infrastructure.entities.AppointmentSchedule;
import com.turnera.turnera.schedule.infrastructure.entities.SpecialSchedule;
import com.turnera.turnera.schedule.infrastructure.entities.VacationSchedule;
import com.turnera.turnera.utils.BuenosAiresTime;
import io.hypersistence.utils.hibernate.type.interval.PostgreSQLIntervalType;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.LinkedHashSet;
import java.util.Set;
import java.util.UUID;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.annotations.Type;

@Setter
@Getter
@Entity
@Table(name = "professional_medical_center_relationship")
@EqualsAndHashCode(onlyExplicitlyIncluded = true)
public class ProfessionalMedicalCenterRelationship {
  @EqualsAndHashCode.Include @EmbeddedId private ProfessionalMedicalCenterRelationshipId id;

  @MapsId("professionalId")
  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "professional_id", nullable = false)
  private Professional professional;

  @Column(name = "professional_id", nullable = false, insertable = false, updatable = false)
  private long professionalId;

  @MapsId("medicalCenterId")
  @ManyToOne(fetch = FetchType.LAZY, optional = false)
  @JoinColumn(name = "medical_center_id", nullable = false)
  private MedicalCenter medicalCenter;

  @Column(name = "overlapped_appointment_limit", columnDefinition = "overlapped_appointment_limit")
  @Enumerated(EnumType.STRING)
  private OverlappedAppointmentLimit overlappedAppointmentLimit;

  @Column(name = "maximum_anticipation_appointment_time_limit", columnDefinition = "INTERVAL")
  @Type(PostgreSQLIntervalType.class)
  private Duration maximumAnticipationAppointmentTimeLimit;

  @Column(name = "minimum_anticipation_appointment_time_limit", columnDefinition = "INTERVAL")
  @Type(PostgreSQLIntervalType.class)
  private Duration minimumAnticipationAppointmentTimeLimit;

  @NotNull
  @Column(name = "appointment_interval_time", nullable = false)
  private LocalTime appointmentIntervalTime;

  @Column private String email;

  @NotNull
  @ColumnDefault("gen_random_uuid()")
  @Column(name = "external_id", nullable = false)
  private UUID externalId;

  @NotNull
  @ManyToOne(fetch = FetchType.LAZY, optional = false)
  @JoinColumn(name = "created_by", nullable = false)
  private EmployeeUser createdBy;

  @NotNull
  @Column(name = "created_at", nullable = false)
  private LocalDateTime createdAt;

  @OneToMany(mappedBy = "professionalMedicalCenterRelationship", fetch = FetchType.LAZY)
  private Set<AppointmentSchedule> appointmentSchedules = new LinkedHashSet<>();

  @OneToMany(mappedBy = "professionalMedicalCenterRelationship", fetch = FetchType.LAZY)
  private Set<SpecialSchedule> specialSchedules = new LinkedHashSet<>();

  @OneToMany(mappedBy = "professionalMedicalCenterRelationship", fetch = FetchType.LAZY)
  private Set<VacationSchedule> vacationSchedules = new LinkedHashSet<>();

  @OneToMany(mappedBy = "professionalMedicalCenterRelationship", fetch = FetchType.LAZY)
  private Set<ConsultationTypeProfessionalMedicalCenterRelationship>
      consultationTypeProfessionalMedicalCenterRelationships = new LinkedHashSet<>();

  @OneToMany(mappedBy = "professionalMedicalCenterRelationship", fetch = FetchType.LAZY)
  private Set<HealthInsuranceProfessionalMedicalCenterRelationship>
      healthInsuranceProfessionalMedicalCenterRelationships = new LinkedHashSet<>();

  public ProfessionalMedicalCenterRelationship() {}

  public ProfessionalMedicalCenterRelationship(
      Professional professional,
      CreateProfessionalMedicalCenterRelationshipInput input,
      EmployeeUser createdBy) {
    MedicalCenter medicalCenter = createdBy.getMedicalCenter(input.getMedicalCenterId());
    this.id =
        new ProfessionalMedicalCenterRelationshipId(professional.getId(), medicalCenter.getId());
    this.professional = professional;
    this.medicalCenter = medicalCenter;
    this.overlappedAppointmentLimit = input.getOverlappedAppointmentLimit();
    this.minimumAnticipationAppointmentTimeLimit =
        input.getMinimumAnticipationAppointmentTimeLimit();
    this.maximumAnticipationAppointmentTimeLimit =
        input.getMaximumAnticipationAppointmentTimeLimit();
    this.appointmentIntervalTime = input.getAppointmentIntervalTime();
    this.email = input.getEmail().orElse(null);
    this.createdBy = createdBy;
    this.createdAt = BuenosAiresTime.nowAsLocalDateTime();
    this.externalId = UUID.randomUUID();
  }

  public void validateRespectsAnticipationTimeLimits(LocalDate date, LocalTime startTime) {
    LocalDateTime appointmentDateTime = LocalDateTime.of(date, startTime);
    LocalDateTime now = BuenosAiresTime.nowAsLocalDateTime();
    LocalDateTime minimumAnticipationDateTime = now.plus(minimumAnticipationAppointmentTimeLimit);
    LocalDateTime maximumAnticipationDateTime = now.plus(maximumAnticipationAppointmentTimeLimit);
    if (appointmentDateTime.isBefore(minimumAnticipationDateTime)) {
      throw new MinimumAnticipationTimeLimitException();
    }
    if (appointmentDateTime.isAfter(maximumAnticipationDateTime)) {
      throw new MaximumAnticipationTimeLimitException();
    }
  }

  public BookingPoliciesDTO toBookingPoliciesDTO() {
    return new BookingPoliciesDTO(
        minimumAnticipationAppointmentTimeLimit.toString(),
        maximumAnticipationAppointmentTimeLimit.toString(),
        overlappedAppointmentLimit.toString());
  }

  public PersonalInformation toPersonalInformation() {
    return PersonalInformation.builder()
        .name(professional.getName())
        .matriculaNacional(professional.getMedicalLicense())
        .email(email)
        .build();
  }
}
