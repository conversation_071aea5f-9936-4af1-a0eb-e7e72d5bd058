package com.turnera.turnera.turneraUser.application.find;

import static com.turnera.turnera.utils.LogMarkers.APPLICATION;

import com.turnera.turnera.turneraUser.domain.TurneraUserService;
import com.turnera.turnera.turneraUser.infrastructure.entities.TurneraUser;
import jakarta.enterprise.context.ApplicationScoped;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@ApplicationScoped
@RequiredArgsConstructor
@Slf4j
public class FindActiveTurneraUserByPhoneNumber {

  private final TurneraUserService turneraUserService;

  public Optional<TurneraUser> find(String phoneNumber) {
    log.info(APPLICATION, "Finding TurneraUser by phone number: {}", phoneNumber);
    Optional<TurneraUser> maybeTurneraUser =
        turneraUserService.findActiveByPhoneNumber(phoneNumber);
    if (maybeTurneraUser.isPresent()) {
      log.info("TurneraUser found with phone number: {}", phoneNumber);
    } else {
      log.info("No <PERSON>aUser found with phone number: {}", phoneNumber);
    }
    return maybeTurneraUser;
  }
}
