package com.turnera.turnera.configuration;

import com.fasterxml.jackson.databind.exc.InvalidFormatException;
import com.turnera.turnera.configuration.exceptions.BadRequestException;
import com.turnera.turnera.configuration.exceptions.ConflictException;
import com.turnera.turnera.configuration.exceptions.ForbiddenException;
import com.turnera.turnera.configuration.exceptions.NotFoundException;
import com.turnera.turnera.configuration.exceptions.UnprocessableEntityException;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.ConstraintViolationException;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import jakarta.ws.rs.ext.ExceptionMapper;
import jakarta.ws.rs.ext.Provider;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.HashMap;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;

@ApplicationScoped
@Provider
@Slf4j
public class GlobalExceptionHandler implements ExceptionMapper<Exception> {

  @Override
  public Response toResponse(Exception exception) {
    return switch (exception) {
      case BadRequestException ex -> handleBadRequestException(ex);
      case NotFoundException ex -> handleNotFoundException(ex);
      case ConflictException ex -> handleConflictException(ex);
      case UnprocessableEntityException ex -> handleUnprocessableEntityException(ex);
      case ForbiddenException ex -> handleForbiddenException(ex);
      case ConstraintViolationException ex -> handleValidationExceptions(ex);
      case IllegalArgumentException ex when ex.getCause() instanceof InvalidFormatException ->
          handleInvalidFormatException((InvalidFormatException) ex.getCause());
      default -> handleGenericException(exception);
    };
  }

  private Response handleInvalidFormatException(InvalidFormatException ex) {
    String errorMessage = "Invalid request format";

    if (ex.getTargetType() != null
        && (ex.getTargetType().equals(LocalDate.class)
            || ex.getTargetType().equals(LocalTime.class))) {
      // Extract just the first part of the error message before the newline
      String originalMessage = ex.getMessage();
      int newlineIndex = originalMessage.indexOf('\n');
      errorMessage =
          newlineIndex > 0 ? originalMessage.substring(0, newlineIndex) : originalMessage;
    } else {
      errorMessage = "Invalid value format";
    }

    Map<String, String> errors = new HashMap<>();
    errors.put("error", errorMessage);
    log.error("Invalid format: {}", errorMessage, ex);
    return Response.status(Response.Status.BAD_REQUEST)
        .entity(errors)
        .type(MediaType.APPLICATION_JSON)
        .build();
  }

  private Response handleValidationExceptions(ConstraintViolationException ex) {
    Map<String, String> errors = new HashMap<>();

    for (ConstraintViolation<?> violation : ex.getConstraintViolations()) {
      String fieldName = violation.getPropertyPath().toString();
      String errorMessage = violation.getMessage();
      errors.put(fieldName, errorMessage);
    }
    log.error("Validation error: {}", errors);
    return Response.status(Response.Status.BAD_REQUEST)
        .entity(errors)
        .type(MediaType.APPLICATION_JSON)
        .build();
  }

  private Response handleBadRequestException(BadRequestException ex) {
    log.error("Bad request: {}", ex.getMessage(), ex);
    return Response.status(Response.Status.BAD_REQUEST)
        .entity(ex.getMessage())
        .type(MediaType.TEXT_PLAIN)
        .build();
  }

  private Response handleNotFoundException(NotFoundException ex) {
    log.error("Not found: {}", ex.getMessage(), ex);
    return Response.status(Response.Status.NOT_FOUND)
        .entity(ex.getMessage())
        .type(MediaType.TEXT_PLAIN)
        .build();
  }

  private Response handleConflictException(ConflictException ex) {
    log.error("Conflict: {}", ex.getMessage(), ex);
    return Response.status(Response.Status.CONFLICT)
        .entity(ex.getMessage())
        .type(MediaType.TEXT_PLAIN)
        .build();
  }

  private Response handleUnprocessableEntityException(UnprocessableEntityException ex) {
    log.error("Unprocessable entity: {}", ex.getMessage(), ex);
    return Response.status(422) // UNPROCESSABLE_ENTITY
        .entity(ex.getMessage())
        .type(MediaType.TEXT_PLAIN)
        .build();
  }

  private Response handleForbiddenException(ForbiddenException ex) {
    log.error("Forbidden: {}", ex.getMessage(), ex);
    return Response.status(Response.Status.FORBIDDEN)
        .entity(ex.getMessage())
        .type(MediaType.TEXT_PLAIN)
        .build();
  }

  private Response handleGenericException(Exception ex) {
    log.error("An unexpected error occurred: {}", ex.getMessage(), ex);
    return Response.status(Response.Status.INTERNAL_SERVER_ERROR)
        .entity("An unexpected error occurred: " + ex.getMessage())
        .type(MediaType.TEXT_PLAIN)
        .build();
  }
}
