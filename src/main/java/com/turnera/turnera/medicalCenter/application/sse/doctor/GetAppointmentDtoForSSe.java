package com.turnera.turnera.medicalCenter.application.sse.doctor;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.turnera.turnera.medicalCenter.infrastructure.entities.MedicalCenter;
import com.turnera.turnera.professional.presentation.entities.ProfessionalInformationDTO;
import com.turnera.turnera.sse.domain.entities.SseEvent;
import com.turnera.turnera.utils.BuenosAiresTime;
import jakarta.enterprise.context.ApplicationScoped;
import java.time.Year;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@ApplicationScoped
@RequiredArgsConstructor
@Slf4j
public class GetAppointmentDtoForDoctor {

  private final ObjectMapper objectMapper;

  public SseEvent get(Integer medicalCenterId) {
    log.info("Fetching professionals for medical center ID: {}", medicalCenterId);
    MedicalCenter medicalCenter =
        medicalCenterValidations.verifyMedicalCenterExists(medicalCenterId);
    List<ProfessionalInformationDTO> professionals =
        findMedicalCenterProfessionalInformation.find(
            medicalCenter,
            BuenosAiresTime.nowAsLocalDate().getMonth(),
            Year.of(BuenosAiresTime.nowAsLocalDate().getYear()));
    JsonNode data = objectMapper.valueToTree(professionals);
    return new SseEvent("doctors", data, System.currentTimeMillis());
  }
}
