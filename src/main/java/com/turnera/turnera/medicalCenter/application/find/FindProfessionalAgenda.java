package com.turnera.turnera.medicalCenter.application.find;

import com.turnera.turnera.appointment.application.find.FindAppointmentsForMedicalCenterAndProfessionalForMonthAndYear;
import com.turnera.turnera.appointment.infrastructure.entities.Appointment;
import com.turnera.turnera.blockedSlot.application.find.FindBlockedSlotsForMedicalCenterAndProfessionalForMonthAndYear;
import com.turnera.turnera.blockedSlot.infrastructure.entities.BlockedSlot;
import com.turnera.turnera.medicalCenter.domain.entities.FindProfessionalScheduleInput;
import com.turnera.turnera.medicalCenter.domain.utils.BuildProfessionalAgendaDtoUtil;
import com.turnera.turnera.professional.infrastructure.entities.ProfessionalMedicalCenterRelationship;
import com.turnera.turnera.professional.presentation.entities.ProfessionalAgendaDTO;
import jakarta.enterprise.context.ApplicationScoped;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@ApplicationScoped
@Slf4j
@RequiredArgsConstructor
public class FindProfessionalAgenda {

  private final FindAppointmentsForMedicalCenterAndProfessionalForMonthAndYear
      findAppointmentsForMedicalCenterAndProfessionalForMonthAndYear;
  private final FindBlockedSlotsForMedicalCenterAndProfessionalForMonthAndYear
      findBlockedSlotsForMedicalCenterAndProfessionalForMonthAndYear;
  private final BuildProfessionalAgendaDtoUtil buildProfessionalAgendaDtoUtil;

  public ProfessionalAgendaDTO find(FindProfessionalScheduleInput input) {
    ProfessionalMedicalCenterRelationship relationship = input.getRelationship();
    log.info(
        "Finding professional schedule for medical center: {}, professional: {}, month: {}, year: {}",
        relationship.getMedicalCenter().getId(),
        relationship.getProfessional().getId(),
        input.getMonth(),
        input.getYear());
    List<Appointment> appointments =
        findAppointmentsForMedicalCenterAndProfessionalForMonthAndYear.find(
            relationship.getMedicalCenter().getId(),
            relationship.getProfessional().getId(),
            input.getMonth(),
            input.getYear());
    List<BlockedSlot> blockedSlots =
        findBlockedSlotsForMedicalCenterAndProfessionalForMonthAndYear.find(
            relationship.getMedicalCenter().getId(),
            relationship.getProfessional().getId(),
            input.getMonth(),
            input.getYear());
    return buildProfessionalAgendaDtoUtil.buildProfessionalScheduleDTO(
        input.getYear(), input.getMonth(), relationship, appointments, blockedSlots);
  }
}
