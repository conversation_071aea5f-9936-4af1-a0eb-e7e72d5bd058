quarkus.application.name=turnera
quarkus.swagger-ui.path=/docs
quarkus.smallrye-openapi.path=/api-docs
quarkus.swagger-ui.always-include=true
quarkus.smallrye-health.ui.enable=true
quarkus.info.build.version=@project.version@
quarkus.jackson.fail-on-unknown-properties=true
quarkus.datasource.db-kind=postgresql
quarkus.datasource.jdbc.url=jdbc:postgresql://${DB_HOST:localhost}/${DB_NAME:turnera}
quarkus.datasource.username=${DB_USER:postgres}
quarkus.datasource.password=${DB_PASSWORD:password}
quarkus.hibernate-orm.database.generation=validate
#logging for sql, uncomment if needs testing
quarkus.hibernate-orm.log.sql=true
quarkus.hibernate-orm.log.bind-parameters=true # Uncomment to log SQL queries
quarkus.http.proxy.proxy-address-forwarding=true
quarkus.http.proxy.allow-forwarded=true
quarkus.log.level=INFO
quarkus.log.category."com.turnera".level=DEBUG
quarkus.http.record-request-start-time=true
quarkus.http.cors=true
quarkus.http.cors.origins=https://www.turnera.com.ar,http://localhost:3000,https://turnera-production-2d268c3f-f25a-4e1a-87a3-f8278ac99d4b.up.railway.app
quarkus.http.cors.methods=GET,POST,PUT,DELETE,OPTIONS,HEAD,PATCH
quarkus.http.cors.headers=accept,authorization,content-type,x-requested-with
quarkus.http.cors.access-control-allow-credentials=true
quarkus.http.cors.access-control-max-age=24H
